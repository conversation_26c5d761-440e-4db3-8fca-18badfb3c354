"""
Utility modules for the multi-agent system
"""

from .colored_logger import (
    setup_colored_logging,
    log_separator,
    log_section_header,
    log_info,
    log_error,
    log_success,
    log_warning,
    Colors
)

# Agent-specific logging functions
def log_user_input(message: str):
    """Log user input with distinctive formatting"""
    print(f"{Colors.BRIGHT_CYAN}👤 USER: {message}{Colors.RESET}")

def log_agent_response(response: str):
    """Log agent response with distinctive formatting"""
    print(f"{Colors.BRIGHT_BLUE}🤖 AGENT: {response}{Colors.RESET}")

def log_tool_call(tool_name: str, input_data: str):
    """Log tool call with distinctive formatting"""
    print(f"{Colors.BRIGHT_MAGENTA}🔧 TOOL CALL: {tool_name}({input_data}){Colors.RESET}")

def log_tool_result(result: str):
    """Log tool result with distinctive formatting"""
    print(f"{Colors.GREEN}📤 TOOL RESULT: {result[:100]}{'...' if len(result) > 100 else ''}{Colors.RESET}")

__all__ = [
    'setup_colored_logging',
    'log_separator',
    'log_section_header',
    'log_info',
    'log_error',
    'log_success',
    'log_warning',
    'log_user_input',
    'log_agent_response',
    'log_tool_call',
    'log_tool_result',
    'Colors'
]
