"""
MongoDB-based Memory System for LangChain
Provides persistent conversation memory using MongoDB collections
"""

import json
import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime

from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointMetadata
from langchain_core.runnables import RunnableConfig
from pymongo import MongoClient
from pymongo.collection import Collection

logger = logging.getLogger(__name__)


class MongoDBCheckpointSaver(BaseCheckpointSaver):
    """
    MongoDB-based checkpoint saver for LangGraph conversation memory
    Stores conversation history in MongoDB collections per tenant
    """
    
    def __init__(self, db: Any, collection_name: str = "conversation_checkpoints"):
        """
        Initialize MongoDB checkpoint saver
        
        Args:
            db: MongoDB database instance
            collection_name: Name of the collection to store checkpoints
        """
        self.db = db
        self.collection_name = collection_name
        self.collection: Collection = db[collection_name]
        
        # Create indexes for better performance
        self.collection.create_index([("thread_id", 1), ("checkpoint_id", 1)])
        self.collection.create_index([("thread_id", 1), ("created_at", -1)])
        
        logger.info(f"✅ MongoDB checkpoint saver initialized with collection: {collection_name}")
    
    def get(self, config: RunnableConfig) -> Optional[Checkpoint]:
        """
        Get the latest checkpoint for a thread
        
        Args:
            config: Runnable configuration containing thread_id
            
        Returns:
            Latest checkpoint or None if not found
        """
        thread_id = config.get("configurable", {}).get("thread_id")
        if not thread_id:
            return None
            
        try:
            # Get the latest checkpoint for this thread
            doc = self.collection.find_one(
                {"thread_id": thread_id},
                sort=[("created_at", -1)]
            )
            
            if not doc:
                return None
                
            # Convert MongoDB document to Checkpoint
            checkpoint_data = json.loads(doc["checkpoint_data"])
            metadata = CheckpointMetadata(
                source="mongodb",
                step=doc.get("step", 0),
                writes=doc.get("writes", {}),
                parents=doc.get("parents", {})
            )
            
            return Checkpoint(
                v=doc.get("version", 1),
                id=doc["checkpoint_id"],
                ts=doc["created_at"].isoformat(),
                channel_values=checkpoint_data.get("channel_values", {}),
                channel_versions=checkpoint_data.get("channel_versions", {}),
                versions_seen=checkpoint_data.get("versions_seen", {}),
                pending_sends=checkpoint_data.get("pending_sends", []),
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Error retrieving checkpoint for thread {thread_id}: {e}")
            return None
    
    def put(self, config: RunnableConfig, checkpoint: Checkpoint, metadata: CheckpointMetadata) -> RunnableConfig:
        """
        Save a checkpoint to MongoDB
        
        Args:
            config: Runnable configuration
            checkpoint: Checkpoint to save
            metadata: Checkpoint metadata
            
        Returns:
            Updated configuration
        """
        thread_id = config.get("configurable", {}).get("thread_id")
        if not thread_id:
            raise ValueError("thread_id is required in config.configurable")
            
        try:
            # Prepare checkpoint data for storage
            checkpoint_data = {
                "channel_values": checkpoint.channel_values,
                "channel_versions": checkpoint.channel_versions,
                "versions_seen": checkpoint.versions_seen,
                "pending_sends": checkpoint.pending_sends
            }
            
            # Create document to store
            doc = {
                "thread_id": thread_id,
                "checkpoint_id": checkpoint.id,
                "version": checkpoint.v,
                "checkpoint_data": json.dumps(checkpoint_data),
                "step": metadata.step,
                "writes": metadata.writes,
                "parents": metadata.parents,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            # Insert or update the checkpoint
            self.collection.replace_one(
                {"thread_id": thread_id, "checkpoint_id": checkpoint.id},
                doc,
                upsert=True
            )
            
            logger.debug(f"Saved checkpoint {checkpoint.id} for thread {thread_id}")
            return config
            
        except Exception as e:
            logger.error(f"Error saving checkpoint for thread {thread_id}: {e}")
            raise
    
    def list(self, config: RunnableConfig, *, filter: Optional[Dict[str, Any]] = None, before: Optional[RunnableConfig] = None, limit: Optional[int] = None) -> List[Tuple[RunnableConfig, Checkpoint, CheckpointMetadata]]:
        """
        List checkpoints for a thread
        
        Args:
            config: Runnable configuration
            filter: Optional filter criteria
            before: Optional before configuration
            limit: Optional limit on number of results
            
        Returns:
            List of (config, checkpoint, metadata) tuples
        """
        thread_id = config.get("configurable", {}).get("thread_id")
        if not thread_id:
            return []
            
        try:
            query = {"thread_id": thread_id}
            
            # Apply additional filters if provided
            if filter:
                query.update(filter)
            
            # Build cursor with sorting
            cursor = self.collection.find(query).sort([("created_at", -1)])
            
            if limit:
                cursor = cursor.limit(limit)
            
            results = []
            for doc in cursor:
                # Convert document to checkpoint
                checkpoint_data = json.loads(doc["checkpoint_data"])
                metadata = CheckpointMetadata(
                    source="mongodb",
                    step=doc.get("step", 0),
                    writes=doc.get("writes", {}),
                    parents=doc.get("parents", {})
                )
                
                checkpoint = Checkpoint(
                    v=doc.get("version", 1),
                    id=doc["checkpoint_id"],
                    ts=doc["created_at"].isoformat(),
                    channel_values=checkpoint_data.get("channel_values", {}),
                    channel_versions=checkpoint_data.get("channel_versions", {}),
                    versions_seen=checkpoint_data.get("versions_seen", {}),
                    pending_sends=checkpoint_data.get("pending_sends", []),
                    metadata=metadata
                )
                
                # Create config for this checkpoint
                checkpoint_config = {
                    "configurable": {
                        "thread_id": thread_id,
                        "checkpoint_id": doc["checkpoint_id"]
                    }
                }
                
                results.append((checkpoint_config, checkpoint, metadata))
            
            return results
            
        except Exception as e:
            logger.error(f"Error listing checkpoints for thread {thread_id}: {e}")
            return []
    
    def get_conversation_history(self, thread_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get conversation history for a thread in a readable format
        
        Args:
            thread_id: Thread identifier
            limit: Maximum number of messages to return
            
        Returns:
            List of conversation messages
        """
        try:
            docs = self.collection.find(
                {"thread_id": thread_id}
            ).sort([("created_at", 1)]).limit(limit)
            
            history = []
            for doc in docs:
                try:
                    checkpoint_data = json.loads(doc["checkpoint_data"])
                    messages = checkpoint_data.get("channel_values", {}).get("messages", [])
                    
                    for message in messages:
                        if isinstance(message, dict):
                            history.append({
                                "timestamp": doc["created_at"],
                                "type": message.get("type", "unknown"),
                                "content": message.get("content", ""),
                                "checkpoint_id": doc["checkpoint_id"]
                            })
                except Exception as e:
                    logger.warning(f"Error parsing checkpoint data: {e}")
                    continue
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting conversation history for thread {thread_id}: {e}")
            return []
    
    def clear_thread_history(self, thread_id: str) -> bool:
        """
        Clear all checkpoints for a specific thread
        
        Args:
            thread_id: Thread identifier
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.collection.delete_many({"thread_id": thread_id})
            logger.info(f"Cleared {result.deleted_count} checkpoints for thread {thread_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing thread history for {thread_id}: {e}")
            return False
