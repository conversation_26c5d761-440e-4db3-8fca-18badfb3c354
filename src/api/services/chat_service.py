"""
Chat Service - Encapsulates agents, tools, and vector DB for dynamic tenant-based chat
Only initializes when needed to reduce unnecessary resource allocation
"""

import logging
from typing import Optional
from langchain_core.tools import tool
from models.user import UserTenantDB
from api.services.agent_v2.main_agent import MainAgentV2
from api.services.agent_v2.search_agent import SearchAgentV2
from api.services.agent_v2.simple_booking_agent import SimpleBookingAgent
from utils import log_info, log_success, log_tool_call, log_tool_result

logger = logging.getLogger(__name__)


class ChatService:
    """
    Chat service that provides access to agents, tools, and vector DB
    Initialized with current user context for dynamic tenant support
    """
    
    def __init__(self, current_user: UserTenantDB):
        """
        Initialize chat service with current user context

        Args:
            current_user: Current user with tenant info and vector store access
        """
        self.current_user = current_user
        self.tenant_id = current_user.tenant_id
        self.vector_store_manager = current_user.vector_store_manager  # Lazy initialization

        # Initialize agents with current user context
        self._main_agent: Optional[MainAgentV2] = None
        self._search_agent: Optional[SearchAgentV2] = None
        self._booking_agent: Optional[SimpleBookingAgent] = None

        # Lazy tool creation - only create when needed
        self._tools: Optional[list] = None

        log_info(f"ChatService initialized for tenant: {self.tenant_id}")

    def _create_tools(self):
        """Create tools with current user context"""

        @tool
        def search_products(user_message: str) -> str:
            """
            Search for products, courses, and educational programs.

            Args:
                user_message: The user's exact message for product search

            Returns:
                Search results for courses/products
            """
            log_tool_call("search_products", f"user_message='{user_message}'")
            try:
                result = self.vector_store_manager.search_products(user_message)
                log_tool_result(result)
                return result
            except Exception as e:
                error_msg = f"Error in product search: {str(e)}"
                log_tool_result(error_msg)
                return error_msg

        @tool
        def search_information(user_message: str) -> str:
            """
            Search for general information, troubleshooting, apps, and services.

            Args:
                user_message: The user's exact message for information search

            Returns:
                Search results for information/troubleshooting queries
            """
            log_tool_call("search_information", f"user_message='{user_message}'")
            try:
                result = self.vector_store_manager.search_information(user_message)
                log_tool_result(result)
                return result
            except Exception as e:
                error_msg = f"Error in information search: {str(e)}"
                log_tool_result(error_msg)
                return error_msg

        @tool
        def handle_booking(user_message: str) -> str:
            """
            Handle booking requests and course enrollment.

            Args:
                user_message: The user's exact message about booking

            Returns:
                Booking response and next steps
            """
            log_tool_call("handle_booking", f"user_message='{user_message}'")
            try:
                thread_id = str(self.current_user.user.id)
                result = self.booking_agent.handle_booking_request(user_message, thread_id)
                log_tool_result(result)
                return result
            except Exception as e:
                error_msg = f"Error handling booking: {str(e)}"
                log_tool_result(error_msg)
                return error_msg

        return [search_products, search_information, handle_booking]

    @property
    def tools(self):
        """Get or create tools with lazy initialization"""
        if self._tools is None:
            log_info("Creating tools with current user context...")
            self._tools = self._create_tools()
            log_info("Tools created successfully")
        return self._tools

    @property
    def main_agent(self) -> MainAgentV2:
        """Get or initialize main agent with current user context"""
        if self._main_agent is None:
            log_info("Initializing Main Agent with current user context...")
            self._main_agent = MainAgentV2(current_user=self.current_user)
            self._main_agent.set_tools(self.tools)  # Set tools with current user context
            log_success("Main Agent initialized successfully with tools")
        return self._main_agent
    
    @property
    def search_agent(self) -> SearchAgentV2:
        """Get or initialize search agent with current user context"""
        if self._search_agent is None:
            log_info(f"Initializing Search Agent for tenant: {self.tenant_id}")
            self._search_agent = SearchAgentV2(tenant_id=self.tenant_id)
            log_success("Search Agent initialized successfully")
        return self._search_agent
    
    @property
    def booking_agent(self) -> SimpleBookingAgent:
        """Get or initialize booking agent with current user context"""
        if self._booking_agent is None:
            log_info("Initializing Booking Agent with current user context...")
            self._booking_agent = SimpleBookingAgent(current_user=self.current_user)
            log_success("Booking Agent initialized successfully")
        return self._booking_agent
    
    def chat(self, message: str, thread_id: str = None) -> dict:
        """
        Process chat message using the main agent
        
        Args:
            message: User's message
            thread_id: Optional thread ID (defaults to user ID)
            
        Returns:
            Dict with response and tools_used
        """
        # Use user ID as thread ID if not provided
        if thread_id is None:
            thread_id = str(self.current_user.user.id)
        
        log_info(f"Processing chat for user {self.current_user.user.username} (tenant: {self.tenant_id})")
        
        # Use main agent to process the message
        return self.main_agent.chat(message, thread_id)
    
    def search_products(self, query: str) -> str:
        """Search for products using current user's vector store"""
        return self.vector_store_manager.search_products(query)
    
    def search_information(self, query: str) -> str:
        """Search for information using current user's vector store"""
        return self.vector_store_manager.search_information(query)
    
    def handle_booking(self, user_message: str, thread_id: str = None) -> str:
        """Handle booking request using current user context"""
        if thread_id is None:
            thread_id = str(self.current_user.user.id)
        
        return self.booking_agent.handle_booking_request(user_message, thread_id)


def create_chat_service(current_user: UserTenantDB) -> ChatService:
    """
    Factory function to create chat service with current user context
    
    Args:
        current_user: Current user with tenant info
        
    Returns:
        ChatService instance configured for the user's tenant
    """
    return ChatService(current_user)
