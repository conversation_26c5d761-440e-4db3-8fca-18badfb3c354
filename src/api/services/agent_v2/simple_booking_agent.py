"""
Simple Booking Agent - Clean booking agent for ChatService
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
import os
from dotenv import load_dotenv

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Simple booking sessions storage
_booking_sessions = {}


class SimpleBookingAgent:
    """
    Simple Booking Agent - Clean booking workflow
    """
    
    def __init__(self, current_user=None):
        """Initialize the booking agent"""
        self.current_user = current_user
        
        # LLM for context understanding
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        
        logger.info("✅ Simple Booking Agent initialized")

    def handle_booking_request(self, user_message: str, thread_id: str) -> str:
        """Handle booking request"""
        try:
            logger.info(f"📅 Booking request: {user_message}")
            
            # Get or create session
            session = self._get_or_create_session(thread_id)
            
            # Use LLM to understand the booking request
            system_prompt = """You are a helpful booking assistant for an educational service center in Nepal.

Help users book courses by:
1. Understanding what course they want to book
2. Collecting their name, email, and phone number
3. Offering available time slots
4. Confirming the booking

Be friendly and guide them through the process step by step.
If they mention a course that doesn't exist, suggest similar alternatives.

Current booking session status: {status}
Collected information: {collected_data}

Respond helpfully to their request."""

            response = self.llm.invoke([
                SystemMessage(content=system_prompt.format(
                    status=session.get("status", "new"),
                    collected_data=json.dumps(session.get("collected_data", {}), indent=2)
                )),
                HumanMessage(content=f"User message: {user_message}")
            ])
            
            # Update session
            session["last_message"] = user_message
            session["updated_at"] = datetime.now().isoformat()
            
            result = response.content
            logger.info(f"✅ Booking response generated")
            return result
            
        except Exception as e:
            error_msg = f"Error handling booking: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble processing your booking request. Please try again or contact our support team."

    def _get_or_create_session(self, thread_id: str) -> Dict:
        """Get existing session or create new one"""
        if thread_id not in _booking_sessions:
            _booking_sessions[thread_id] = {
                "status": "new",
                "collected_data": {},
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "course_name": None,
                "is_incomplete": False
            }
        return _booking_sessions[thread_id]

    def has_pending_booking(self, thread_id: str) -> Dict[str, Any]:
        """Check if user has a pending/incomplete booking"""
        if thread_id in _booking_sessions:
            session = _booking_sessions[thread_id]

            # Check if booking is incomplete (started but not finished)
            collected = session.get("collected_data", {})
            has_course = session.get("course_name") or collected.get("course")
            has_contact = collected.get("name") and collected.get("email")

            if has_course and not has_contact and session.get("status") != "completed":
                return {
                    "has_pending": True,
                    "course_name": session.get("course_name") or collected.get("course"),
                    "missing_info": self._get_missing_info(collected),
                    "last_updated": session.get("updated_at")
                }

        return {"has_pending": False}

    def _get_missing_info(self, collected_data: Dict) -> list:
        """Get list of missing required information"""
        missing = []
        if not collected_data.get("name"):
            missing.append("name")
        if not collected_data.get("email"):
            missing.append("email")
        if not collected_data.get("phone"):
            missing.append("phone number")
        return missing

    def get_pending_booking_reminder(self, thread_id: str) -> str:
        """Get reminder message for pending booking"""
        pending = self.has_pending_booking(thread_id)
        if pending["has_pending"]:
            missing = ", ".join(pending["missing_info"])
            return f"\n\n📝 **Reminder**: You have an incomplete booking for '{pending['course_name']}'. We still need your {missing} to complete the enrollment. Would you like to continue with the booking?"
        return ""
