from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends
from pydantic import BaseModel


from api.services.chat_service import create_chat_service
from core.security import get_tenant_info
from models.user import UserTenantDB
from utils import setup_colored_logging, log_info, log_error, log_success

# Setup logging
setup_colored_logging()

router = APIRouter(tags=["Chat"])


class ChatRequest(BaseModel):
    message: str


class ToolUsed(BaseModel):
    name: str
    description: str
    input: dict = {}
    output: str = ""

class ChatResponse(BaseModel):
    response: str
    thread_id: str
    user_id: str
    tools_used: list[ToolUsed] = []


@router.post("/chat", response_model=ChatResponse)
async def chat(
    chat_request: ChatRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Chat endpoint that uses ChatService with current user context
    The user ID from the token is used as the thread ID for conversation continuity
    """
    try:
        # Create chat service with current user context (includes vector store, agents, tools)
        chat_service = create_chat_service(current_user)

        # Use user ID as thread ID for conversation continuity per user
        thread_id = str(current_user.user.id)
        user_id = str(current_user.user.id)

        log_info(f"Chat request from user {current_user.user.username} (ID: {user_id}, tenant: {current_user.tenant_id}): {chat_request.message[:50]}...")

        # Get response from chat service (automatically uses current user's vector store and agents)
        agent_response = chat_service.chat(chat_request.message, thread_id)

        log_success(f"Chat response generated for user {current_user.user.username}")

        # Handle both old string format and new dict format for backward compatibility
        if isinstance(agent_response, dict):
            response_text = agent_response.get("response", "")
            tools_used = [ToolUsed(**tool) for tool in agent_response.get("tools_used", [])]
        else:
            response_text = agent_response
            tools_used = []

        return ChatResponse(
            response=response_text,
            thread_id=thread_id,
            user_id=user_id,
            tools_used=tools_used
        )
        
    except Exception as e:
        log_error(f"Chat failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail="Failed to process chat request"
        )


@router.get("/chat/health")
async def chat_health_check():
    """
    Health check endpoint for chat service
    """
    return {
        "status": "healthy",
        "service": "chat",
        "message": "Chat service is running with ChatService architecture"
    }
