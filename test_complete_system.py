#!/usr/bin/env python3
"""
Complete System Test - Test all functionality end-to-end
Tests chat, search, booking, confirmation, and cancellation
"""

import sys
import os
import asyncio
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from api.services.chat_service import create_chat_service
from models.user import UserTenantDB, User
from config import get_vector_store_manager

def create_test_user():
    """Create a test user for testing"""
    # Create a mock user
    user = User(
        id="test_user_123",
        username="test_user",
        email="<EMAIL>",
        tenant_id="ambition-guru"
    )
    
    # Create UserTenantDB with vector store manager
    user_tenant = UserTenantDB(
        user=user,
        tenant_id="ambition-guru"
    )
    
    # Initialize vector store manager
    user_tenant._vector_store_manager = get_vector_store_manager("ambition-guru")
    
    return user_tenant

def test_chat_service_creation():
    """Test 1: ChatService creation"""
    print("🧪 Test 1: ChatService Creation")
    try:
        user = create_test_user()
        chat_service = create_chat_service(user)
        print(f"✅ ChatService created successfully for tenant: {chat_service.tenant_id}")
        return chat_service
    except Exception as e:
        print(f"❌ ChatService creation failed: {e}")
        return None

def test_information_search(chat_service):
    """Test 2: Information search functionality"""
    print("\n🧪 Test 2: Information Search")
    try:
        # Test direct search
        result = chat_service.search_information("app not working troubleshooting")
        print(f"✅ Information search successful")
        print(f"📄 Result preview: {result[:200]}...")
        
        # Test through chat agent
        chat_result = chat_service.chat("My app is not working, can you help?")
        print(f"✅ Chat-based information search successful")
        print(f"🤖 Agent response: {chat_result['response'][:200]}...")
        return True
    except Exception as e:
        print(f"❌ Information search failed: {e}")
        return False

def test_product_search(chat_service):
    """Test 3: Product search functionality"""
    print("\n🧪 Test 3: Product Search")
    try:
        # Test direct search
        result = chat_service.search_products("Python programming course")
        print(f"✅ Product search successful")
        print(f"📄 Result preview: {result[:200]}...")
        
        # Test through chat agent
        chat_result = chat_service.chat("What Python courses do you have?")
        print(f"✅ Chat-based product search successful")
        print(f"🤖 Agent response: {chat_result['response'][:200]}...")
        return True
    except Exception as e:
        print(f"❌ Product search failed: {e}")
        return False

def test_booking_flow(chat_service):
    """Test 4: Complete booking flow"""
    print("\n🧪 Test 4: Booking Flow")
    
    test_scenarios = [
        "I want to book a Python course",
        "My name is John Doe",
        "My <NAME_EMAIL>", 
        "My phone is 9841234567",
        "I prefer morning slots",
        "Yes, please confirm my booking"
    ]
    
    try:
        thread_id = "test_booking_123"
        
        for i, message in enumerate(test_scenarios, 1):
            print(f"\n📝 Step {i}: {message}")
            result = chat_service.chat(message, thread_id)
            print(f"🤖 Response: {result['response'][:300]}...")
            
            if result.get('tools_used'):
                print(f"🔧 Tools used: {[tool['name'] for tool in result['tools_used']]}")
        
        print(f"✅ Booking flow completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Booking flow failed: {e}")
        return False

def test_booking_management(chat_service):
    """Test 5: Booking management (show/cancel)"""
    print("\n🧪 Test 5: Booking Management")
    
    management_scenarios = [
        "Show me my bookings",
        "I want to cancel my booking",
        "What are my upcoming appointments?"
    ]
    
    try:
        thread_id = "test_management_456"
        
        for i, message in enumerate(management_scenarios, 1):
            print(f"\n📝 Management {i}: {message}")
            result = chat_service.chat(message, thread_id)
            print(f"🤖 Response: {result['response'][:300]}...")
            
            if result.get('tools_used'):
                print(f"🔧 Tools used: {[tool['name'] for tool in result['tools_used']]}")
        
        print(f"✅ Booking management completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Booking management failed: {e}")
        return False

def test_error_handling(chat_service):
    """Test 6: Error handling and edge cases"""
    print("\n🧪 Test 6: Error Handling")
    
    error_scenarios = [
        "Book a course that doesn't exist",
        "Invalid email format test@",
        "Empty message: ",
        "Very long message: " + "x" * 1000
    ]
    
    try:
        for i, message in enumerate(error_scenarios, 1):
            print(f"\n🔍 Error test {i}: {message[:50]}...")
            try:
                result = chat_service.chat(message, f"error_test_{i}")
                print(f"✅ Handled gracefully: {result['response'][:200]}...")
            except Exception as e:
                print(f"⚠️ Exception caught: {str(e)[:200]}...")
        
        print(f"✅ Error handling tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Error handling tests failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Complete System Test")
    print("=" * 50)
    
    # Test 1: ChatService creation
    chat_service = test_chat_service_creation()
    if not chat_service:
        print("❌ Cannot proceed without ChatService")
        return
    
    # Test 2: Information search
    info_success = test_information_search(chat_service)
    
    # Test 3: Product search  
    product_success = test_product_search(chat_service)
    
    # Test 4: Booking flow
    booking_success = test_booking_flow(chat_service)
    
    # Test 5: Booking management
    management_success = test_booking_management(chat_service)
    
    # Test 6: Error handling
    error_success = test_error_handling(chat_service)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    tests = [
        ("ChatService Creation", True),
        ("Information Search", info_success),
        ("Product Search", product_success), 
        ("Booking Flow", booking_success),
        ("Booking Management", management_success),
        ("Error Handling", error_success)
    ]
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for test_name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<20} {status}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
