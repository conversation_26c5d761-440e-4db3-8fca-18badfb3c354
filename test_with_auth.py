#!/usr/bin/env python3
"""
Test with proper authentication and real API endpoints
Tests login, then chat functionality with real user session
"""

import requests
import json
import time
import sys
import os

# Configuration
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "username": "agent1",
    "password": "agent123",
    "client_id": "ambition-guru"
}

class APITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.token = None
        self.headers = {"Content-Type": "application/json"}
        
    def login(self):
        """Test 1: Login and get authentication token"""
        print("🧪 Test 1: User Authentication")
        try:
            login_data = {
                "username": TEST_USER["username"],
                "password": TEST_USER["password"],
                "client_id": TEST_USER["client_id"]
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/login",
                data=login_data  # FastAPI expects form data for OAuth2
            )
            
            if response.status_code == 200:
                result = response.json()
                self.token = result["access_token"]
                self.headers["Authorization"] = f"Bearer {self.token}"
                print(f"✅ Login successful")
                print(f"🔑 Token received: {self.token[:20]}...")
                return True
            else:
                print(f"❌ Login failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    def test_chat_health(self):
        """Test 2: Chat service health check"""
        print("\n🧪 Test 2: Chat Service Health")
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/chat/health",
                headers=self.headers
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Chat service healthy")
                print(f"📊 Status: {result}")
                return True
            else:
                print(f"❌ Chat health check failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Chat health error: {e}")
            return False
    
    def send_chat_message(self, message, test_name=""):
        """Send a chat message and return response"""
        try:
            chat_data = {"message": message}
            
            response = requests.post(
                f"{self.base_url}/api/v1/chat",
                headers=self.headers,
                json=chat_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {test_name} successful")
                print(f"💬 Message: {message}")
                print(f"🤖 Response: {result['response'][:300]}...")
                if result.get('tools_used'):
                    tools = [tool['name'] for tool in result['tools_used']]
                    print(f"🔧 Tools used: {tools}")
                print("-" * 50)
                return result
            else:
                print(f"❌ {test_name} failed: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
            return None
    
    def test_information_queries(self):
        """Test 3: Information search queries"""
        print("\n🧪 Test 3: Information Search Queries")
        
        info_queries = [
            "My app is not working, can you help?",
            "How do I troubleshoot login issues?",
            "What services do you provide?",
            "App crashing on startup"
        ]
        
        success_count = 0
        for i, query in enumerate(info_queries, 1):
            result = self.send_chat_message(query, f"Info Query {i}")
            if result:
                success_count += 1
            time.sleep(1)  # Small delay between requests
        
        print(f"📊 Information queries: {success_count}/{len(info_queries)} successful")
        return success_count == len(info_queries)
    
    def test_product_queries(self):
        """Test 4: Product search queries"""
        print("\n🧪 Test 4: Product Search Queries")
        
        product_queries = [
            "What Python courses do you have?",
            "Show me programming courses",
            "I want to learn web development",
            "Do you have IELTS preparation courses?"
        ]
        
        success_count = 0
        for i, query in enumerate(product_queries, 1):
            result = self.send_chat_message(query, f"Product Query {i}")
            if result:
                success_count += 1
            time.sleep(1)
        
        print(f"📊 Product queries: {success_count}/{len(product_queries)} successful")
        return success_count == len(product_queries)
    
    def test_booking_flow(self):
        """Test 5: Complete booking flow"""
        print("\n🧪 Test 5: Complete Booking Flow")
        
        booking_steps = [
            "I want to book a Python programming course",
            "My name is John Doe",
            "My <NAME_EMAIL>",
            "My phone number is 9841234567",
            "I prefer morning time slots",
            "Yes, please confirm my booking"
        ]
        
        success_count = 0
        for i, step in enumerate(booking_steps, 1):
            result = self.send_chat_message(step, f"Booking Step {i}")
            if result:
                success_count += 1
            time.sleep(1)
        
        print(f"📊 Booking steps: {success_count}/{len(booking_steps)} successful")
        return success_count == len(booking_steps)
    
    def test_booking_management(self):
        """Test 6: Booking management"""
        print("\n🧪 Test 6: Booking Management")
        
        management_queries = [
            "Show me my current bookings",
            "What are my upcoming appointments?",
            "I want to cancel my booking",
            "Can I reschedule my appointment?"
        ]
        
        success_count = 0
        for i, query in enumerate(management_queries, 1):
            result = self.send_chat_message(query, f"Management Query {i}")
            if result:
                success_count += 1
            time.sleep(1)
        
        print(f"📊 Management queries: {success_count}/{len(management_queries)} successful")
        return success_count == len(management_queries)
    
    def test_error_scenarios(self):
        """Test 7: Error handling scenarios"""
        print("\n🧪 Test 7: Error Handling")
        
        error_scenarios = [
            "Book a course that doesn't exist like XYZ123",
            "",  # Empty message
            "a" * 2500,  # Very long message (over 2000 limit)
            "Invalid email format: notanemail"
        ]
        
        success_count = 0
        for i, scenario in enumerate(error_scenarios, 1):
            if scenario == "":
                print(f"💬 Message: [EMPTY MESSAGE]")
            else:
                print(f"💬 Message: {scenario[:50]}...")
            
            result = self.send_chat_message(scenario, f"Error Test {i}")
            if result:  # Should handle gracefully, not crash
                success_count += 1
            time.sleep(1)
        
        print(f"📊 Error scenarios: {success_count}/{len(error_scenarios)} handled gracefully")
        return success_count == len(error_scenarios)

def main():
    """Run all tests with proper authentication"""
    print("🚀 Starting Complete System Test with Authentication")
    print("=" * 60)
    print(f"🌐 Testing against: {BASE_URL}")
    print(f"👤 Test user: {TEST_USER['username']}")
    print("=" * 60)
    
    tester = APITester()
    
    # Test 1: Authentication
    if not tester.login():
        print("❌ Cannot proceed without authentication")
        return
    
    # Test 2: Chat health
    health_success = tester.test_chat_health()
    
    # Test 3: Information queries
    info_success = tester.test_information_queries()
    
    # Test 4: Product queries
    product_success = tester.test_product_queries()
    
    # Test 5: Booking flow
    booking_success = tester.test_booking_flow()
    
    # Test 6: Booking management
    management_success = tester.test_booking_management()
    
    # Test 7: Error handling
    error_success = tester.test_error_scenarios()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPLETE TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("User Authentication", True),
        ("Chat Service Health", health_success),
        ("Information Queries", info_success),
        ("Product Queries", product_success),
        ("Booking Flow", booking_success),
        ("Booking Management", management_success),
        ("Error Handling", error_success)
    ]
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for test_name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<25} {status}")
    
    print(f"\nFinal Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is fully functional.")
        print("✅ Chat, search, booking, and management all working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        print("🔧 System may need debugging before production use.")

if __name__ == "__main__":
    main()
